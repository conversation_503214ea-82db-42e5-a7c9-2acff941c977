<template>
  <div class="editor-test-container">
    <h2>增强版富文本编辑器功能测试</h2>
    
    <div class="test-section">
      <h3>编辑器组件</h3>
      <Editor v-model="editorContent" ref="editor" />
    </div>
    
    <div class="test-section">
      <h3>功能测试按钮</h3>
      <el-row :gutter="10">
        <el-col :span="6">
          <el-button type="primary" @click="insertSampleTable" icon="el-icon-s-grid">插入示例表格</el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" @click="insertSampleContent" icon="el-icon-document">插入示例内容</el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" @click="clearContent" icon="el-icon-delete">清空内容</el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" @click="showCurrentContent" icon="el-icon-view">显示当前内容</el-button>
        </el-col>
      </el-row>
      
      <el-row :gutter="10" style="margin-top: 10px;">
        <el-col :span="8">
          <el-button @click="testTableOperations" icon="el-icon-s-operation">测试表格操作</el-button>
        </el-col>
        <el-col :span="8">
          <el-button @click="testSourceCodeMode" icon="el-icon-edit-outline">测试源代码模式</el-button>
        </el-col>
        <el-col :span="8">
          <el-button @click="insertComplexHTML" icon="el-icon-magic-stick">插入复杂HTML</el-button>
        </el-col>
      </el-row>
    </div>
    
    <div class="test-section">
      <h3>当前内容预览</h3>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="HTML源代码" name="source">
          <pre class="source-code">{{ editorContent }}</pre>
        </el-tab-pane>
        <el-tab-pane label="渲染效果" name="preview">
          <div class="rendered-content" v-html="editorContent"></div>
        </el-tab-pane>
        <el-tab-pane label="统计信息" name="stats">
          <div class="stats-info">
            <p><strong>字符数：</strong>{{ editorContent.length }}</p>
            <p><strong>包含表格：</strong>{{ hasTable ? '是' : '否' }}</p>
            <p><strong>包含图片：</strong>{{ hasImage ? '是' : '否' }}</p>
            <p><strong>包含链接：</strong>{{ hasLink ? '是' : '否' }}</p>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import Editor from '@/components/Editor/index.vue'

export default {
  name: 'EditorTest',
  components: {
    Editor
  },
  data() {
    return {
      editorContent: '<p>欢迎使用增强版富文本编辑器！</p><p><strong>新功能包括：</strong></p><ul><li>完整的表格功能（插入、编辑、删除）</li><li>HTML源代码编辑模式</li><li>可视化与源代码模式切换</li><li>表格操作菜单</li></ul>',
      activeTab: 'source'
    }
  },
  computed: {
    hasTable() {
      return this.editorContent.includes('<table')
    },
    hasImage() {
      return this.editorContent.includes('<img')
    },
    hasLink() {
      return this.editorContent.includes('<a ')
    }
  },
  methods: {
    insertSampleTable() {
      const tableHTML = `
        <table style="border-collapse: collapse; width: 100%; border: 1px solid #000; margin: 10px 0;">
          <tr>
            <th style="border: 1px solid #000; padding: 8px; background-color: #f0f0f0; font-weight: bold;">姓名</th>
            <th style="border: 1px solid #000; padding: 8px; background-color: #f0f0f0; font-weight: bold;">年龄</th>
            <th style="border: 1px solid #000; padding: 8px; background-color: #f0f0f0; font-weight: bold;">职位</th>
            <th style="border: 1px solid #000; padding: 8px; background-color: #f0f0f0; font-weight: bold;">部门</th>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 8px;">张三</td>
            <td style="border: 1px solid #000; padding: 8px;">25</td>
            <td style="border: 1px solid #000; padding: 8px;">前端开发工程师</td>
            <td style="border: 1px solid #000; padding: 8px;">技术部</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 8px;">李四</td>
            <td style="border: 1px solid #000; padding: 8px;">30</td>
            <td style="border: 1px solid #000; padding: 8px;">后端开发工程师</td>
            <td style="border: 1px solid #000; padding: 8px;">技术部</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 8px;">王五</td>
            <td style="border: 1px solid #000; padding: 8px;">28</td>
            <td style="border: 1px solid #000; padding: 8px;">产品经理</td>
            <td style="border: 1px solid #000; padding: 8px;">产品部</td>
          </tr>
        </table>
        <p><br></p>
      `;
      this.editorContent += tableHTML;
    },
    
    insertSampleContent() {
      const sampleContent = `
        <h2 style="color: #1890ff;">功能演示文档</h2>
        <p>这是一个包含多种格式的演示文档，展示富文本编辑器的各种功能。</p>
        
        <h3>文本格式化</h3>
        <p>支持 <strong>粗体</strong>、<em>斜体</em>、<u>下划线</u>、<span style="color: red;">彩色文本</span> 等格式。</p>
        
        <h3>列表功能</h3>
        <ol>
          <li>有序列表项目1</li>
          <li>有序列表项目2</li>
          <li>有序列表项目3</li>
        </ol>
        
        <ul>
          <li>无序列表项目A</li>
          <li>无序列表项目B</li>
          <li>无序列表项目C</li>
        </ul>
        
        <blockquote>这是一个引用块，用于突出显示重要信息或引用他人的话。</blockquote>
        
        <h3>代码块</h3>
        <pre>function hello() {
    console.log("Hello, World!");
}</pre>
        
        <p>更多功能等待您的探索！</p>
      `;
      this.editorContent += sampleContent;
    },
    
    clearContent() {
      this.$confirm('确定要清空所有内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.editorContent = '';
        this.$message.success('内容已清空');
      }).catch(() => {
        this.$message.info('已取消清空操作');
      });
    },
    
    showCurrentContent() {
      this.$alert(this.editorContent, '当前编辑器内容', {
        confirmButtonText: '确定',
        type: 'info'
      });
    },
    
    testTableOperations() {
      this.$message.info('请在编辑器中插入表格，然后右键点击表格单元格查看操作菜单');
    },
    
    testSourceCodeMode() {
      this.$message.info('请点击编辑器上方的"源代码编辑"按钮切换到HTML源代码编辑模式');
    },
    
    insertComplexHTML() {
      const complexHTML = `
        <div style="border: 2px solid #1890ff; padding: 15px; border-radius: 8px; background-color: #f0f8ff;">
          <h4 style="color: #1890ff; margin-top: 0;">复杂HTML内容示例</h4>
          <p>这是一个包含复杂HTML结构的示例，包括：</p>
          <ul>
            <li>带样式的容器</li>
            <li>嵌套的HTML元素</li>
            <li><a href="https://example.com" target="_blank">外部链接</a></li>
            <li><span style="background-color: yellow; padding: 2px 4px;">高亮文本</span></li>
          </ul>
          <p style="text-align: center; font-style: italic;">居中斜体文本</p>
        </div>
        <p><br></p>
      `;
      this.editorContent += complexHTML;
    }
  }
}
</script>

<style lang="less" scoped>
.editor-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  
  h3 {
    color: #409EFF;
    border-bottom: 2px solid #409EFF;
    padding-bottom: 5px;
    margin-bottom: 15px;
  }
}

.source-code {
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.rendered-content {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 15px;
  background-color: white;
  min-height: 100px;
  max-height: 400px;
  overflow-y: auto;
  
  /deep/ table {
    margin: 10px 0;
  }
}

.stats-info {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  
  p {
    margin: 8px 0;
    font-size: 14px;
  }
}

.el-button {
  width: 100%;
  margin-bottom: 5px;
}
</style>
