<template>
  <div style="padding-bottom: 10px">
    <!-- 图片上传组件辅助 -->
    <el-upload
      class="avatar-uploader quill-img"
      :action="uploadImgUrl"
      name="file"
      :headers="headers"
      :show-file-list="false"
      :on-success="quillImgSuccess"
      :on-error="uploadError"
      :before-upload="quillImgBefore"
      accept=".jpg,.jpeg,.png,.gif"
      v-show="false"
    ></el-upload>

    <!-- 富文本组件 -->
    <quill-editor
      class="editor"
      v-model="content"
      ref="quillEditor"
      :options="editorOption"
      @blur="onEditorBlur($event)"
      @focus="onEditorFocus($event)"
      @change="onEditorChange($event)"
    ></quill-editor>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import Quill from "quill";
import htmlEditButton from "quill-html-edit-button";
Quill.register({
  "modules/htmlEditButton": htmlEditButton,
});

// 工具栏配置
const toolbarOptions = [
  ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
  ["blockquote", "code-block"], // 引用  代码块
  [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
  [{ indent: "-1" }, { indent: "+1" }], // 缩进
  [{ size: ["small", false, "large", "huge"] }], // 字体大小
  [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
  [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
  [
    {
      font: [
        "SimSun",
        "SimHei",
        "Microsoft-YaHei",
        "KaiTi",
        "FangSong",
        "Arial",
        "Times-New-Roman",
        "sans-serif",
      ],
    },
  ], // 字体
  [{ align: [] }], // 对齐方式
  ["clean"], // 清除文本格式
  ["link", "image", "video"], // 链接、图片、视频
  ["table"], // 表格按钮
];

import { quillEditor } from "vue-quill-editor";
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";

// 字体白名单
const Font = Quill.import("formats/font");
Font.whitelist = [
  "SimSun",
  "SimHei",
  "Microsoft-YaHei",
  "KaiTi",
  "FangSong",
  "Arial",
  "Times-New-Roman",
  "sans-serif",
];
Quill.register(Font, true);

export default {
  props: {
    /* 编辑器的内容 */
    value: {
      type: String,
    },
    /* 图片大小 */
    maxSize: {
      type: Number,
      default: 4000, // kb
    },
  },
  components: { quillEditor },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      content: this.value,
      editorOption: {
        theme: "snow", // or 'bubble'
        placeholder: "请输入内容",
        modules: {
          // HTML编辑按钮模块配置
          htmlEditButton: {
            debug: false, // 调试模式
            msg: "编辑HTML源代码", // 按钮提示文本
            okText: "确定", // 确定按钮文本
            cancelText: "取消", // 取消按钮文本
            buttonHTML: "&lt;&gt;", // 按钮HTML
            buttonTitle: "显示HTML源代码", // 按钮标题
            syntax: false, // 是否启用语法高亮
            prependSelector: 'div[id^="quill-html-edit-button"]', // CSS选择器前缀
            editorModules: {}, // 编辑器模块配置
          },
          toolbar: {
            container: toolbarOptions,
            handlers: {
              image: function(value) {
                if (value) {
                  // 触发input框选择图片文件
                  document.querySelector(".quill-img input").click();
                } else {
                  this.quill.format("image", false);
                }
              },
              table: function(value) {
                if (value) {
                  // 插入3x3表格
                  const quill = this.quill;
                  const range = quill.getSelection();
                  if (range) {
                    let tableHTML =
                      '<table style="border-collapse: collapse; width: 100%; border: 1px solid #ccc; margin: 10px 0;">';

                    for (let i = 0; i < 3; i++) {
                      tableHTML += "<tr>";
                      for (let j = 0; j < 3; j++) {
                        const cellTag = i === 0 ? "th" : "td";
                        const cellStyle =
                          "border: 1px solid #ccc; padding: 8px; text-align: left; min-width: 50px;";
                        const bgStyle =
                          i === 0
                            ? " background-color: #f5f5f5; font-weight: bold;"
                            : "";
                        tableHTML += `<${cellTag} style="${cellStyle}${bgStyle}">${
                          i === 0 ? `列${j + 1}` : "&nbsp;"
                        }</${cellTag}>`;
                      }
                      tableHTML += "</tr>";
                    }
                    tableHTML += "</table><p><br></p>";

                    quill.clipboard.dangerouslyPasteHTML(
                      range.index,
                      tableHTML
                    );
                    quill.setSelection(range.index + tableHTML.length);
                  }
                }
              },
            },
          },
        },
      },
      uploadImgUrl: baseUrl + "/upload/file", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
    };
  },
  watch: {
    value: function() {
      this.content = this.value;
    },
  },
  methods: {
    onEditorBlur() {
      // 失去焦点事件
    },
    onEditorFocus() {
      // 获得焦点事件
    },
    onEditorChange() {
      // 内容改变事件
      this.$emit("input", this.content);
    },

    /**
     * class转style，适用于邮件等场景
     * 用法：this.fontClassToStyle(html)
     * 建议在需要发送邮件或导出时调用，不要影响编辑器内部内容
     */
    fontClassToStyle(html) {
      const fontMap = {
        "ql-font-SimSun": "font-family: SimSun, serif;",
        "ql-font-SimHei": "font-family: SimHei, sans-serif;",
        "ql-font-Microsoft-YaHei": "font-family: Microsoft YaHei, sans-serif;",
        "ql-font-KaiTi": "font-family: KaiTi, serif;",
        "ql-font-FangSong": "font-family: FangSong, serif;",
        "ql-font-Arial": "font-family: Arial, sans-serif;",
        "ql-font-Times-New-Roman": "font-family: Times New Roman, serif;",
        "ql-font-sans-serif": "font-family: sans-serif;",
      };
      return html.replace(
        /class="([^"]*?ql-font-[^"]*?)"/g,
        (match, classNames) => {
          let style = "";
          classNames.split(" ").forEach((cls) => {
            if (fontMap[cls]) style += fontMap[cls];
          });
          if (style) {
            // 保留原有class（如果有其他class），并加上style
            return match + ` style="${style}"`;
          }
          return match;
        }
      );
    },

    // 富文本图片上传前
    quillImgBefore(file) {
      const fileType = file.type;
      if (fileType === "image/jpeg" || fileType === "image/png") {
        return true;
      } else {
        this.$message.error("请插入图片类型文件(jpg/jpeg/png)");
        return false;
      }
    },

    quillImgSuccess(res) {
      // res为图片服务器返回的数据
      // 获取富文本组件实例
      const quill = this.$refs.quillEditor.quill;
      // 如果上传成功
      if (res.code == 10000) {
        // 获取光标所在位置
        const length = quill.getSelection().index;
        // 插入图片  res.url为服务器返回的图片地址
        quill.insertEmbed(length, "image", res.data);
        // 调整光标到最后
        quill.setSelection(length + 1);
      } else {
        this.$message.error("图片插入失败");
      }
    },
    // 富文本图片上传失败
    uploadError() {
      // loading动画消失
      this.$message.error("图片插入失败");
    },
  },
};
</script>

<style lang="less">
.editor {
  line-height: normal !important;
  height: 192px;
}
/*.el-upload {*/
/*  display: none;*/
/*}*/

.ql-container {
  max-height: 160px;
  overflow: auto;
  .ql-tooltip {
    left: 0 !important;
  }
}
.ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "请输入链接地址:";
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "保存";
  padding-right: 0px;
}
.ql-editor {
  /* overflow: hidden; */
}

.ql-snow .ql-tooltip[data-mode="video"]::before {
  content: "请输入视频地址:";
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "14px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
  content: "10px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
  content: "18px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
  content: "32px";
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "文本";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "标题1";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "标题2";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "标题3";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "标题4";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "标题5";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "标题6";
}

.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: "标准字体";
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="SimSun"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="SimSun"]::before {
  content: "宋体";
  font-family: "SimSun", serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="SimHei"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="SimHei"]::before {
  content: "黑体";
  font-family: "SimHei", sans-serif;
}
.ql-snow
  .ql-picker.ql-font
  .ql-picker-label[data-value="Microsoft-YaHei"]::before,
.ql-snow
  .ql-picker.ql-font
  .ql-picker-item[data-value="Microsoft-YaHei"]::before {
  content: "微软雅黑";
  font-family: "Microsoft YaHei", sans-serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="KaiTi"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="KaiTi"]::before {
  content: "楷体";
  font-family: "KaiTi", serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="FangSong"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="FangSong"]::before {
  content: "仿宋";
  font-family: "FangSong", serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Arial"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Arial"]::before {
  content: "Arial";
  font-family: "Arial", sans-serif;
}
.ql-snow
  .ql-picker.ql-font
  .ql-picker-label[data-value="Times-New-Roman"]::before,
.ql-snow
  .ql-picker.ql-font
  .ql-picker-item[data-value="Times-New-Roman"]::before {
  content: "Times New Roman";
  font-family: "Times New Roman", serif;
}

.ql-font-SimSun {
  font-family: "SimSun", serif;
}
.ql-font-SimHei {
  font-family: "SimHei", sans-serif;
}
.ql-font-Microsoft-YaHei {
  font-family: "Microsoft YaHei", sans-serif;
}
.ql-font-KaiTi {
  font-family: "KaiTi", serif;
}
.ql-font-FangSong {
  font-family: "FangSong", serif;
}
.ql-font-Arial {
  font-family: "Arial", sans-serif;
}
.ql-font-Times-New-Roman {
  font-family: "Times New Roman", serif;
}
.ql-font-sans-serif {
  font-family: sans-serif;
}

/* HTML编辑按钮样式 */
.ql-snow .ql-toolbar .ql-html {
  width: 28px;
  height: 28px;
}

.ql-snow .ql-toolbar button.ql-html {
  width: 28px;
  height: 28px;
}

.ql-snow .ql-toolbar button.ql-html:before {
  content: "<>";
  font-size: 12px;
  font-weight: bold;
}

/* 表格按钮样式 */
.ql-snow .ql-toolbar button.ql-table {
  width: 28px;
  height: 28px;
}

.ql-snow .ql-toolbar button.ql-table:before {
  content: "⊞";
  font-size: 16px;
}

/* 编辑器内表格样式 */
.ql-editor table {
  border-collapse: collapse;
  border: 1px solid #ccc;
  table-layout: fixed;
  width: 100%;
  margin: 10px 0;
}

.ql-editor table td,
.ql-editor table th {
  border: 1px solid #ccc;
  padding: 8px;
  vertical-align: top;
  text-align: left;
  min-width: 50px;
}

.ql-editor table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

/* HTML编辑对话框样式优化 */
.ql-html-overlayContainer {
  z-index: 9999 !important;
}

.ql-html-textContainer {
  border: 1px solid #ccc;
  border-radius: 4px;
}

.ql-html-textArea {
  font-family: "Courier New", Courier, monospace !important;
  font-size: 13px !important;
  line-height: 1.4 !important;
  border: none !important;
  outline: none !important;
  resize: vertical !important;
}
</style>
