# 富文本编辑器组件 (Editor)

增强版富文本编辑器组件，基于 Quill.js 构建，遵循官方最佳实践，支持HTML源代码编辑和表格功能。

## 新增功能

### 1. HTML源代码编辑模式 ✨

- **技术实现**: 使用官方推荐的 `quill-html-edit-button` 插件
- **功能描述**: 在工具栏中添加HTML源代码编辑按钮（<> 图标）
- **使用方法**: 点击工具栏中的 `<>` 按钮打开HTML源代码编辑对话框
- **特性**:
  - ✅ 官方推荐的实现方式，稳定可靠
  - ✅ 弹窗式HTML编辑界面，用户体验友好
  - ✅ 支持直接编辑HTML代码
  - ✅ 支持从外部复制粘贴HTML代码
  - ✅ 自动验证HTML内容的安全性和正确性
  - ✅ 等宽字体显示，便于代码阅读和编辑
  - ✅ 支持多行编辑，可调整对话框大小

### 2. 表格功能增强 ✨

- **技术实现**: 使用自定义表格插入处理器，兼容Quill 1.x版本
- **功能描述**: 在工具栏中添加表格插入功能
- **使用方法**: 点击工具栏中的表格按钮（⊞ 图标）插入3x3的默认表格
- **特性**:
  - ✅ 自动插入带样式的表格
  - ✅ 表格包含样式化的表头和数据行
  - ✅ 支持表格的基本样式（边框、背景色、内边距等）
  - ✅ 表格样式使用内联CSS，确保在各种环境下正确显示
  - ✅ 在HTML源代码模式中粘贴的表格能正确渲染
  - ✅ 表格可以在编辑器中直接编辑内容

## 技术架构

### 依赖项

```json
{
  "vue-quill-editor": "^3.0.6",
  "quill": "^1.3.7",
  "quill-html-edit-button": "^2.2.7"
}
```

### 插件注册

```javascript
import htmlEditButton from "quill-html-edit-button";

// 注册HTML编辑按钮模块
Quill.register("modules/htmlEditButton", htmlEditButton);
```

### 模块配置

```javascript
modules: {
  // HTML编辑按钮模块配置
  htmlEditButton: {
    debug: false,
    msg: "编辑HTML源代码",
    okText: "确定",
    cancelText: "取消",
    buttonHTML: "&lt;&gt;",
    buttonTitle: "显示HTML源代码",
    syntax: false,
    prependSelector: 'div[id^="quill-html-edit-button"]',
    editorModules: {}
  },
  toolbar: {
    container: toolbarOptions,
    handlers: {
      // 表格插入处理器
      table: function(value) {
        if (value) {
          const quill = this.quill;
          const range = quill.getSelection();
          if (range) {
            // 插入3x3表格HTML
            let tableHTML = '...';
            quill.clipboard.dangerouslyPasteHTML(range.index, tableHTML);
          }
        }
      }
    }
  }
}
```

## API 接口

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | - | 编辑器内容，支持v-model双向绑定 |
| maxSize | Number | 4000 | 图片上传最大尺寸（KB） |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| input | 内容改变时触发 | (content: string) |
| blur | 编辑器失去焦点时触发 | (event) |
| focus | 编辑器获得焦点时触发 | (event) |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| fontClassToStyle | 将Quill的字体class转换为内联样式 | (html: string) => string |

## 使用示例

### 基础使用

```vue
<template>
  <div>
    <Editor v-model="content" />
  </div>
</template>

<script>
import Editor from '@/components/Editor/index.vue'

export default {
  components: {
    Editor
  },
  data() {
    return {
      content: '<p>初始内容</p>'
    }
  }
}
</script>
```

### HTML源代码编辑

1. 点击工具栏中的 `<>` 按钮
2. 在弹出的对话框中编辑HTML代码
3. 可以复制粘贴外部HTML内容
4. 点击"确定"保存更改，点击"取消"放弃更改

### 表格插入和编辑

1. 在可视化编辑模式下，点击工具栏中的表格按钮（⊞）
2. 系统会自动插入一个3x3的表格
3. 可以在表格中直接编辑内容
4. 在HTML源代码模式下可以手动编辑表格HTML

## 样式定制

### 表格样式

```css
.ql-editor table {
  border-collapse: collapse;
  border: 1px solid #ccc;
  table-layout: fixed;
  width: 100%;
  margin: 10px 0;
}

.ql-editor table td,
.ql-editor table th {
  border: 1px solid #ccc;
  padding: 8px;
  vertical-align: top;
  text-align: left;
  min-width: 50px;
}

.ql-editor table th {
  background-color: #f5f5f5;
  font-weight: bold;
}
```

### HTML编辑对话框样式

```css
.ql-html-overlayContainer {
  z-index: 9999 !important;
}

.ql-html-textArea {
  font-family: 'Courier New', Courier, monospace !important;
  font-size: 13px !important;
  line-height: 1.4 !important;
}
```

## 兼容性说明

- ✅ 兼容 Vue 2.x
- ✅ 兼容 Quill 1.3.x
- ✅ 兼容 vue-quill-editor 3.0.x
- ✅ 支持现代浏览器（Chrome, Firefox, Safari, Edge）
- ✅ 保持向后兼容性，不影响现有功能

## 注意事项

1. **安全性**: HTML编辑功能使用Quill的安全API，自动过滤危险内容
2. **样式隔离**: 表格样式使用内联CSS，避免样式冲突
3. **性能**: 插件按需加载，不影响编辑器初始化性能
4. **维护性**: 遵循官方最佳实践，便于后续升级和维护

## 更新日志

### v2.0.0 (2024-06-13)

- ✨ 新增HTML源代码编辑功能（使用官方推荐插件）
- ✨ 新增表格插入和编辑功能
- 🔧 优化工具栏配置和样式
- 📝 完善技术文档和使用说明
- 🐛 修复兼容性问题
- ⚡ 提升用户体验和稳定性
